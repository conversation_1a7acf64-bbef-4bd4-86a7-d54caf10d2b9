"""
简化的增强vHeat模型测试
不依赖外部库，仅使用PyTorch核心功能
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import time

class SimpleThermalRadiationModule(nn.Module):
    """简化的热辐射模块：实现通道间的温度交互"""
    
    def __init__(self, channels, temperature_scale=1.0, radiation_strength=0.1, alpha=1.0):
        super().__init__()
        self.channels = channels
        self.temperature_scale = temperature_scale
        self.radiation_strength = radiation_strength
        self.alpha = alpha
        
        # 学习每个通道的辐射系数（类似物理中的发射率ε）
        self.emissivity = nn.Parameter(torch.ones(channels))
        
        # 通道间交互的权重矩阵
        self.channel_interaction = nn.Linear(channels, channels, bias=False)
        
        # 自适应温度预测网络
        self.temperature_predictor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, max(channels // 4, 1), 1),
            nn.ReLU(),
            nn.Conv2d(max(channels // 4, 1), channels, 1),
            nn.Sigmoid()
        )
        
    def compute_channel_temperatures(self, x):
        """计算每个通道的全局平均温度"""
        # x: [B, C, H, W] -> temperatures: [B, C]
        temperatures = torch.mean(x, dim=[2, 3])  # 全局平均池化
        
        # 预测每个通道的温度权重
        temp_weights = self.temperature_predictor(x).squeeze(-1).squeeze(-1)  # [B, C]
        
        # 应用温度权重和缩放
        adaptive_temperatures = temperatures * temp_weights * self.temperature_scale
        return adaptive_temperatures
    
    def thermal_radiation_exchange(self, temperatures):
        """模拟热辐射交换过程"""
        B, C = temperatures.shape
        
        # Stefan-Boltzmann辐射定律的简化版本
        # P_i = ε_i * σ * T_i^4 (每个通道的辐射功率)
        radiation_power = self.emissivity.unsqueeze(0) * torch.pow(temperatures.abs() + 1e-8, 4)
        
        # 通道间的辐射交换矩阵
        # 温度差越大，交换越强烈
        temp_diff_matrix = temperatures.unsqueeze(2) - temperatures.unsqueeze(1)  # [B, C, C]
        exchange_weights = torch.softmax(-self.alpha * torch.abs(temp_diff_matrix), dim=-1)
        
        # 计算每个通道接收到的辐射能量
        received_energy = torch.bmm(exchange_weights, radiation_power.unsqueeze(-1)).squeeze(-1)
        
        return received_energy * self.radiation_strength
    
    def forward(self, x):
        """前向传播"""
        B, C, H, W = x.shape
        
        # 1. 计算通道温度
        temperatures = self.compute_channel_temperatures(x)
        
        # 2. 热辐射交换
        radiation_adjustment = self.thermal_radiation_exchange(temperatures)
        
        # 3. 应用辐射调整到特征图
        # 将标量调整广播到整个特征图
        adjustment_map = radiation_adjustment.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]
        
        # 4. 通道间信息交互
        channel_weights = self.channel_interaction(temperatures)  # [B, C]
        channel_weights = torch.softmax(channel_weights, dim=1)
        
        # 5. 融合原始特征和辐射调整
        x_adjusted = x + adjustment_map
        x_reweighted = x_adjusted * channel_weights.unsqueeze(-1).unsqueeze(-1)
        
        return x_reweighted


def test_thermal_radiation_module():
    """测试热辐射模块"""
    print("=== 测试热辐射模块 ===")
    
    # 创建热辐射模块
    channels = 96
    thermal_module = SimpleThermalRadiationModule(
        channels=channels,
        temperature_scale=1.0,
        radiation_strength=0.1
    )
    
    # 创建测试数据
    batch_size = 2
    height, width = 56, 56
    test_features = torch.randn(batch_size, channels, height, width)
    
    print(f"输入特征形状: {test_features.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in thermal_module.parameters()):,}")
    
    # 测试前向传播
    thermal_module.eval()
    with torch.no_grad():
        # 计算通道温度
        temperatures = thermal_module.compute_channel_temperatures(test_features)
        print(f"通道温度形状: {temperatures.shape}")
        print(f"温度统计: 均值={temperatures.mean().item():.4f}, "
              f"标准差={temperatures.std().item():.4f}, "
              f"范围=[{temperatures.min().item():.4f}, {temperatures.max().item():.4f}]")
        
        # 热辐射交换
        radiation_energy = thermal_module.thermal_radiation_exchange(temperatures)
        print(f"辐射能量形状: {radiation_energy.shape}")
        print(f"辐射能量统计: 均值={radiation_energy.mean().item():.4f}, "
              f"标准差={radiation_energy.std().item():.4f}")
        
        # 完整前向传播
        enhanced_features = thermal_module(test_features)
        print(f"增强特征形状: {enhanced_features.shape}")
        
        # 计算特征变化
        feature_change = torch.norm(enhanced_features - test_features, dim=[2, 3]).mean()
        print(f"特征变化幅度: {feature_change.item():.6f}")


def test_different_scenarios():
    """测试不同场景下的热辐射行为"""
    print("\n=== 测试不同场景 ===")
    
    channels = 64
    thermal_module = SimpleThermalRadiationModule(
        channels=channels,
        temperature_scale=1.0,
        radiation_strength=0.1
    )
    
    batch_size = 2
    height, width = 32, 32
    
    # 场景1：均匀分布
    uniform_features = torch.randn(batch_size, channels, height, width) * 0.5
    
    # 场景2：高对比度分布
    contrast_features = torch.randn(batch_size, channels, height, width)
    contrast_features[:, :channels//2] *= 2.0  # 前半部分通道温度更高
    
    # 场景3：稀疏激活
    sparse_features = torch.randn(batch_size, channels, height, width) * 0.1
    sparse_features[:, channels//4:channels//2] *= 5.0  # 只有部分通道高激活
    
    scenarios = [
        ("均匀分布", uniform_features),
        ("高对比度", contrast_features),
        ("稀疏激活", sparse_features)
    ]
    
    thermal_module.eval()
    with torch.no_grad():
        for name, features in scenarios:
            print(f"\n--- {name} ---")
            
            # 计算通道温度
            temperatures = thermal_module.compute_channel_temperatures(features)
            print(f"温度统计: 均值={temperatures.mean().item():.4f}, "
                  f"标准差={temperatures.std().item():.4f}, "
                  f"范围=[{temperatures.min().item():.4f}, {temperatures.max().item():.4f}]")
            
            # 热辐射交换
            radiation_energy = thermal_module.thermal_radiation_exchange(temperatures)
            print(f"辐射能量统计: 均值={radiation_energy.mean().item():.4f}, "
                  f"标准差={radiation_energy.std().item():.4f}")
            
            # 完整前向传播
            enhanced_features = thermal_module(features)
            
            # 计算变化
            feature_change = torch.norm(enhanced_features - features, dim=[2, 3]).mean()
            print(f"特征变化幅度: {feature_change.item():.6f}")


def benchmark_performance():
    """性能基准测试"""
    print("\n=== 性能基准测试 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 测试不同通道数的性能
    test_configs = [
        (32, 112, 112),   # 小模型
        (96, 56, 56),     # 中等模型
        (192, 28, 28),    # 大模型
        (384, 14, 14),    # 很大模型
    ]
    
    batch_size = 4
    
    for channels, height, width in test_configs:
        print(f"\n--- 测试配置: C={channels}, H={height}, W={width} ---")
        
        # 创建模块
        thermal_module = SimpleThermalRadiationModule(
            channels=channels,
            radiation_strength=0.1
        ).to(device)
        
        # 创建测试数据
        test_input = torch.randn(batch_size, channels, height, width).to(device)
        
        # 计算参数数量
        total_params = sum(p.numel() for p in thermal_module.parameters())
        print(f"参数数量: {total_params:,}")
        
        # 测试推理时间
        thermal_module.eval()
        warmup_runs = 10
        test_runs = 50
        
        # 预热
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = thermal_module(test_input)
        
        # 正式测试
        if device.type == 'cuda':
            torch.cuda.synchronize()
        
        start_time = time.time()
        with torch.no_grad():
            for _ in range(test_runs):
                output = thermal_module(test_input)
                if device.type == 'cuda':
                    torch.cuda.synchronize()
        
        end_time = time.time()
        avg_time = (end_time - start_time) / test_runs
        
        print(f"平均推理时间: {avg_time*1000:.3f}ms")
        print(f"吞吐量: {batch_size/avg_time:.1f} samples/sec")
        
        # 计算内存使用
        if device.type == 'cuda':
            memory_used = torch.cuda.max_memory_allocated() / 1024**2  # MB
            print(f"GPU内存使用: {memory_used:.1f}MB")
            torch.cuda.reset_peak_memory_stats()


def test_gradient_flow():
    """测试梯度流"""
    print("\n=== 测试梯度流 ===")
    
    channels = 64
    thermal_module = SimpleThermalRadiationModule(
        channels=channels,
        radiation_strength=0.1
    )
    
    # 创建测试数据
    batch_size = 2
    test_input = torch.randn(batch_size, channels, 32, 32, requires_grad=True)
    
    # 前向传播
    output = thermal_module(test_input)
    
    # 计算损失（简单的L2损失）
    target = torch.randn_like(output)
    loss = F.mse_loss(output, target)
    
    # 反向传播
    loss.backward()
    
    # 检查梯度
    print(f"输入梯度范数: {test_input.grad.norm().item():.6f}")
    
    # 检查模块参数梯度
    for name, param in thermal_module.named_parameters():
        if param.grad is not None:
            grad_norm = param.grad.norm().item()
            print(f"{name} 梯度范数: {grad_norm:.6f}")
        else:
            print(f"{name} 无梯度")
    
    print("梯度流测试完成")


def main():
    """主测试函数"""
    print("增强vHeat热辐射模块测试")
    print("="*50)
    
    # 1. 基础功能测试
    test_thermal_radiation_module()
    
    # 2. 不同场景测试
    test_different_scenarios()
    
    # 3. 性能基准测试
    benchmark_performance()
    
    # 4. 梯度流测试
    test_gradient_flow()
    
    print("\n所有测试完成！")


if __name__ == "__main__":
    main()
