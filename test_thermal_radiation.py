#!/usr/bin/env python3
"""
测试修正后的ThermalRadiationModule热辐射模块
验证是否符合物理原理
"""

import torch
import numpy as np
import matplotlib.pyplot as plt
from classification.models.vHeat import ThermalRadiationModule


def test_energy_conservation():
    """测试能量守恒定律"""
    print("=== 测试能量守恒定律 ===")
    
    # 创建热辐射模块
    channels = 8
    thermal_module = ThermalRadiationModule(
        channels=channels,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 创建测试温度（不同的温度分布）
    batch_size = 3
    test_cases = [
        torch.tensor([[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0]]),  # 递增温度
        torch.tensor([[8.0, 7.0, 6.0, 5.0, 4.0, 3.0, 2.0, 1.0]]),  # 递减温度
        torch.tensor([[5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 5.0, 5.0]]),  # 相同温度
    ]
    
    for i, temperatures in enumerate(test_cases):
        print(f"\n测试案例 {i+1}:")
        print(f"输入温度: {temperatures.squeeze().tolist()}")
        
        # 计算热辐射交换
        net_energy_change = thermal_module.thermal_radiation_exchange(temperatures)
        
        print(f"净能量变化: {net_energy_change.squeeze().tolist()}")
        
        # 验证能量守恒
        total_energy_change = torch.sum(net_energy_change, dim=1)
        print(f"总能量变化: {total_energy_change.item():.6f}")
        
        # 检查是否接近0（能量守恒）
        is_conserved = torch.abs(total_energy_change) < 1e-6
        print(f"能量守恒: {'✓' if is_conserved.item() else '✗'}")


def test_heat_flow_direction():
    """测试热流方向：能量应该从高温流向低温"""
    print("\n=== 测试热流方向 ===")
    
    thermal_module = ThermalRadiationModule(
        channels=4,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 创建简单的温度梯度：高温 -> 低温
    temperatures = torch.tensor([[10.0, 7.0, 4.0, 1.0]])
    print(f"温度分布: {temperatures.squeeze().tolist()}")
    
    net_energy_change = thermal_module.thermal_radiation_exchange(temperatures)
    print(f"净能量变化: {net_energy_change.squeeze().tolist()}")
    
    # 验证：最高温通道应该失去能量（负值），最低温通道应该获得能量（正值）
    highest_temp_idx = torch.argmax(temperatures, dim=1)
    lowest_temp_idx = torch.argmin(temperatures, dim=1)
    
    highest_temp_change = net_energy_change[0, highest_temp_idx]
    lowest_temp_change = net_energy_change[0, lowest_temp_idx]
    
    print(f"最高温通道({highest_temp_idx.item()})能量变化: {highest_temp_change.item():.6f}")
    print(f"最低温通道({lowest_temp_idx.item()})能量变化: {lowest_temp_change.item():.6f}")
    
    print(f"最高温通道失去能量: {'✓' if highest_temp_change < 0 else '✗'}")
    print(f"最低温通道获得能量: {'✓' if lowest_temp_change > 0 else '✗'}")


def test_temperature_difference_effect():
    """测试温度差效应：温度差越大，交换越强烈"""
    print("\n=== 测试温度差效应 ===")
    
    thermal_module = ThermalRadiationModule(
        channels=3,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 测试不同温度差的情况
    test_cases = [
        torch.tensor([[5.0, 5.1, 5.2]]),  # 小温度差
        torch.tensor([[5.0, 6.0, 7.0]]),  # 中等温度差
        torch.tensor([[5.0, 8.0, 11.0]]), # 大温度差
    ]
    
    energy_changes = []
    
    for i, temperatures in enumerate(test_cases):
        temp_diff = temperatures.max() - temperatures.min()
        print(f"\n温度差 {temp_diff.item():.1f}: {temperatures.squeeze().tolist()}")
        
        net_energy_change = thermal_module.thermal_radiation_exchange(temperatures)
        print(f"净能量变化: {net_energy_change.squeeze().tolist()}")
        
        # 计算能量变化的强度（绝对值的平均）
        energy_intensity = torch.mean(torch.abs(net_energy_change))
        energy_changes.append(energy_intensity.item())
        print(f"能量交换强度: {energy_intensity.item():.6f}")
    
    # 验证：温度差越大，能量交换越强烈
    print(f"\n能量交换强度趋势: {energy_changes}")
    is_increasing = all(energy_changes[i] <= energy_changes[i+1] for i in range(len(energy_changes)-1))
    print(f"温度差越大交换越强烈: {'✓' if is_increasing else '✗'}")


def test_no_self_radiation():
    """测试无自辐射：通道不应该向自己辐射"""
    print("\n=== 测试无自辐射 ===")
    
    thermal_module = ThermalRadiationModule(
        channels=3,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 创建只有一个通道有不同温度的情况
    temperatures = torch.tensor([[5.0, 5.0, 10.0]])  # 只有最后一个通道温度不同
    print(f"温度分布: {temperatures.squeeze().tolist()}")
    
    net_energy_change = thermal_module.thermal_radiation_exchange(temperatures)
    print(f"净能量变化: {net_energy_change.squeeze().tolist()}")
    
    # 前两个通道温度相同，它们之间不应该有净能量交换
    # 但它们都应该从第三个高温通道获得能量
    print(f"相同温度通道间的交换: 通道0和1应该获得相似的能量")
    print(f"高温通道应该失去能量: {'✓' if net_energy_change[0, 2] < 0 else '✗'}")


def visualize_thermal_exchange():
    """可视化热交换过程"""
    print("\n=== 可视化热交换过程 ===")
    
    thermal_module = ThermalRadiationModule(
        channels=8,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 创建温度梯度
    temperatures = torch.tensor([[1.0, 2.0, 3.0, 4.0, 5.0, 6.0, 7.0, 8.0]])
    net_energy_change = thermal_module.thermal_radiation_exchange(temperatures)
    
    # 绘制结果
    channels = range(len(temperatures.squeeze()))
    temp_values = temperatures.squeeze().detach().numpy()
    energy_values = net_energy_change.squeeze().detach().numpy()
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(10, 8))
    
    # 温度分布
    ax1.bar(channels, temp_values, alpha=0.7, color='red')
    ax1.set_title('通道温度分布')
    ax1.set_xlabel('通道索引')
    ax1.set_ylabel('温度')
    ax1.grid(True, alpha=0.3)
    
    # 净能量变化
    colors = ['blue' if x > 0 else 'orange' for x in energy_values]
    ax2.bar(channels, energy_values, alpha=0.7, color=colors)
    ax2.set_title('净能量变化 (蓝色=获得能量, 橙色=失去能量)')
    ax2.set_xlabel('通道索引')
    ax2.set_ylabel('净能量变化')
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('thermal_radiation_test.png', dpi=150, bbox_inches='tight')
    print("可视化结果已保存为 'thermal_radiation_test.png'")


def test_full_module():
    """测试完整的热辐射模块"""
    print("\n=== 测试完整的热辐射模块 ===")
    
    thermal_module = ThermalRadiationModule(
        channels=64,
        temperature_scale=1.0,
        radiation_strength=0.1,
        alpha=1.0
    )
    
    # 创建测试特征图
    batch_size = 2
    channels = 64
    height, width = 32, 32
    
    test_features = torch.randn(batch_size, channels, height, width)
    print(f"输入特征形状: {test_features.shape}")
    
    # 前向传播
    enhanced_features = thermal_module(test_features)
    print(f"输出特征形状: {enhanced_features.shape}")
    
    # 验证输出合理性
    print(f"输入特征范围: [{test_features.min().item():.4f}, {test_features.max().item():.4f}]")
    print(f"输出特征范围: [{enhanced_features.min().item():.4f}, {enhanced_features.max().item():.4f}]")
    
    # 检查是否有梯度
    loss = enhanced_features.sum()
    loss.backward()
    
    has_grad = any(p.grad is not None and p.grad.abs().sum() > 0 for p in thermal_module.parameters())
    print(f"参数有梯度: {'✓' if has_grad else '✗'}")


if __name__ == "__main__":
    print("开始测试修正后的ThermalRadiationModule...")
    
    # 运行所有测试
    test_energy_conservation()
    test_heat_flow_direction()
    test_temperature_difference_effect()
    test_no_self_radiation()
    test_full_module()
    
    # 可视化（如果matplotlib可用）
    try:
        visualize_thermal_exchange()
    except ImportError:
        print("\n注意: matplotlib未安装，跳过可视化测试")
    
    print("\n=== 所有测试完成 ===")
