import time
import math
from functools import partial
from typing import Optional, Callable

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.utils.checkpoint as checkpoint

# 简化的DropPath实现
class DropPath(nn.Module):
    """Drop paths (Stochastic Depth) per sample (when applied in main path of residual blocks)."""
    def __init__(self, drop_prob=None):
        super(DropPath, self).__init__()
        self.drop_prob = drop_prob

    def forward(self, x):
        if self.drop_prob == 0. or not self.training:
            return x
        keep_prob = 1 - self.drop_prob
        shape = (x.shape[0],) + (1,) * (x.ndim - 1)  # work with diff dim tensors, not just 2D ConvNets
        random_tensor = keep_prob + torch.rand(shape, dtype=x.dtype, device=x.device)
        random_tensor.floor_()  # binarize
        output = x.div(keep_prob) * random_tensor
        return output

    def __repr__(self):
        return f"DropPath({self.drop_prob})"

# 简化的权重初始化函数
def trunc_normal_(tensor, mean=0., std=1., a=-2., b=2.):
    """Truncated normal initialization"""
    with torch.no_grad():
        tensor.normal_(mean, std)
        tensor.clamp_(min=a, max=b)
    return tensor

def to_2tuple(x):
    """Convert to 2-tuple"""
    if isinstance(x, (list, tuple)):
        return tuple(x)
    return (x, x)


class LayerNorm2d(nn.LayerNorm):
    def forward(self, x: torch.Tensor):
        x = x.permute(0, 2, 3, 1).contiguous()
        x = F.layer_norm(x, self.normalized_shape, self.weight, self.bias, self.eps)
        x = x.permute(0, 3, 1, 2).contiguous()
        return x


class to_channels_first(nn.Module):

    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x.permute(0, 3, 1, 2).contiguous()


class to_channels_last(nn.Module):

    def __init__(self):
        super().__init__()

    def forward(self, x):
        return x.permute(0, 2, 3, 1).contiguous()
    
    
def build_norm_layer(dim,
                     norm_layer,
                     in_format='channels_last',
                     out_format='channels_last',
                     eps=1e-6):
    layers = []
    if norm_layer == 'BN':
        if in_format == 'channels_last':
            layers.append(to_channels_first())
        layers.append(nn.BatchNorm2d(dim))
        if out_format == 'channels_last':
            layers.append(to_channels_last())
    elif norm_layer == 'LN':
        if in_format == 'channels_first':
            layers.append(to_channels_last())
        layers.append(nn.LayerNorm(dim, eps=eps))
        if out_format == 'channels_first':
            layers.append(to_channels_first())
    else:
        raise NotImplementedError(
            f'build_norm_layer does not support {norm_layer}')
    return nn.Sequential(*layers)


def build_act_layer(act_layer):
    if act_layer == 'ReLU':
        return nn.ReLU(inplace=True)
    elif act_layer == 'SiLU':
        return nn.SiLU(inplace=True)
    elif act_layer == 'GELU':
        return nn.GELU()

    raise NotImplementedError(f'build_act_layer does not support {act_layer}')
    
    
class StemLayer(nn.Module):
    r""" Stem layer of InternImage
    Args:
        in_chans (int): number of input channels
        out_chans (int): number of output channels
        act_layer (str): activation layer
        norm_layer (str): normalization layer
    """

    def __init__(self,
                 in_chans=3,
                 out_chans=96,
                 act_layer='GELU',
                 norm_layer='BN'):
        super().__init__()
        self.conv1 = nn.Conv2d(in_chans,
                               out_chans // 2,
                               kernel_size=3,
                               stride=2,
                               padding=1)
        self.norm1 = build_norm_layer(out_chans // 2, norm_layer,
                                      'channels_first', 'channels_first')
        self.act = build_act_layer(act_layer)
        self.conv2 = nn.Conv2d(out_chans // 2,
                               out_chans,
                               kernel_size=3,
                               stride=2,
                               padding=1)
        self.norm2 = build_norm_layer(out_chans, norm_layer, 'channels_first',
                                      'channels_first')

    def forward(self, x):
        x = self.conv1(x)
        x = self.norm1(x)
        x = self.act(x)
        x = self.conv2(x)
        x = self.norm2(x)
        return x
    

class Mlp(nn.Module):
    def __init__(self, in_features, hidden_features=None, out_features=None, act_layer=nn.GELU, drop=0.,channels_first=False):
        super().__init__()
        out_features = out_features or in_features
        hidden_features = hidden_features or in_features

        Linear = partial(nn.Conv2d, kernel_size=1, padding=0) if channels_first else nn.Linear
        self.fc1 = Linear(in_features, hidden_features)
        self.act = act_layer()
        self.fc2 = Linear(hidden_features, out_features)
        self.drop = nn.Dropout(drop)

    def forward(self, x):
        x = self.fc1(x)
        x = self.act(x)
        x = self.drop(x)
        x = self.fc2(x)
        x = self.drop(x)
        return x


class Heat2D(nn.Module):
    """
    du/dt -k(d2u/dx2 + d2u/dy2) = 0;
    du/dx_{x=0, x=a} = 0
    du/dy_{y=0, y=b} = 0
    =>
    A_{n, m} = C(a, b, n==0, m==0) * sum_{0}^{a}{ sum_{0}^{b}{\phi(x, y)cos(n\pi/ax)cos(m\pi/by)dxdy }}
    core = cos(n\pi/ax)cos(m\pi/by)exp(-[(n\pi/a)^2 + (m\pi/b)^2]kt)
    u_{x, y, t} = sum_{0}^{\infinite}{ sum_{0}^{\infinite}{ core } }
    
    assume a = N, b = M; x in [0, N], y in [0, M]; n in [0, N], m in [0, M]; with some slight change
    => 
    (\phi(x, y) = linear(dwconv(input(x, y))))
    A(n, m) = DCT2D(\phi(x, y))
    u(x, y, t) = IDCT2D(A(n, m) * exp(-[(n\pi/a)^2 + (m\pi/b)^2])**kt)
    """    
    def __init__(self, infer_mode=False, res=14, dim=96, hidden_dim=96, **kwargs):
        super().__init__()
        self.res = res
        self.dwconv = nn.Conv2d(dim, hidden_dim, kernel_size=3, padding=1, groups=hidden_dim)
        self.hidden_dim = hidden_dim
        self.linear = nn.Linear(hidden_dim, 2 * hidden_dim, bias=True)
        self.out_norm = nn.LayerNorm(hidden_dim)
        self.out_linear = nn.Linear(hidden_dim, hidden_dim, bias=True)
        self.infer_mode = infer_mode
        self.to_k = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim, bias=True),
            nn.ReLU(),
        )
    
    def infer_init_heat2d(self, freq):
        weight_exp = self.get_decay_map((self.res, self.res), device=freq.device)
        self.k_exp = nn.Parameter(torch.pow(weight_exp[:, :, None], self.to_k(freq)), requires_grad=False)
        del self.to_k

    @staticmethod
    def get_cos_map(N=224, device=torch.device("cpu"), dtype=torch.float):
        # cos((x + 0.5) / N * n * \pi) which is also the form of DCT and IDCT
        # DCT: F(n) = sum( (sqrt(2/N) if n > 0 else sqrt(1/N)) * cos((x + 0.5) / N * n * \pi) * f(x) )
        # IDCT: f(x) = sum( (sqrt(2/N) if n > 0 else sqrt(1/N)) * cos((x + 0.5) / N * n * \pi) * F(n) )
        # returns: (Res_n, Res_x)
        weight_x = (torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(1, -1) + 0.5) / N
        weight_n = torch.linspace(0, N - 1, N, device=device, dtype=dtype).view(-1, 1)
        weight = torch.cos(weight_n * weight_x * torch.pi) * math.sqrt(2 / N)
        weight[0, :] = weight[0, :] / math.sqrt(2)
        return weight

    @staticmethod
    def get_decay_map(resolution=(224, 224), device=torch.device("cpu"), dtype=torch.float):
        # exp(-[(n\pi/a)^2 + (m\pi/b)^2])
        # returns: (Res_h, Res_w)
        resh, resw = resolution
        weight_n = torch.linspace(0, torch.pi, resh + 1, device=device, dtype=dtype)[:resh].view(-1, 1)
        weight_m = torch.linspace(0, torch.pi, resw + 1, device=device, dtype=dtype)[:resw].view(1, -1)
        weight = torch.pow(weight_n, 2) + torch.pow(weight_m, 2)
        weight = torch.exp(-weight)
        return weight

    def forward(self, x: torch.Tensor, freq_embed=None):
        B, C, H, W = x.shape
        x = self.dwconv(x)
        
        x = self.linear(x.permute(0, 2, 3, 1).contiguous()) # B, H, W, 2C
        x, z = x.chunk(chunks=2, dim=-1) # B, H, W, C

        if ((H, W) == getattr(self, "__RES__", (0, 0))) and (getattr(self, "__WEIGHT_COSN__", None).device == x.device):
            weight_cosn = getattr(self, "__WEIGHT_COSN__", None)
            weight_cosm = getattr(self, "__WEIGHT_COSM__", None)
            weight_exp = getattr(self, "__WEIGHT_EXP__", None)
            assert weight_cosn is not None
            assert weight_cosm is not None
            assert weight_exp is not None
        else:
            weight_cosn = self.get_cos_map(H, device=x.device).detach_()
            weight_cosm = self.get_cos_map(W, device=x.device).detach_()
            weight_exp = self.get_decay_map((H, W), device=x.device).detach_()
            setattr(self, "__RES__", (H, W))
            setattr(self, "__WEIGHT_COSN__", weight_cosn)
            setattr(self, "__WEIGHT_COSM__", weight_cosm)
            setattr(self, "__WEIGHT_EXP__", weight_exp)

        N, M = weight_cosn.shape[0], weight_cosm.shape[0]
        
        x = F.conv1d(x.contiguous().view(B, H, -1), weight_cosn.contiguous().view(N, H, 1))
        x = F.conv1d(x.contiguous().view(-1, W, C), weight_cosm.contiguous().view(M, W, 1)).contiguous().view(B, N, M, -1)
        
        if self.infer_mode:
            x = torch.einsum("bnmc,nmc->bnmc", x, self.k_exp)
        else:
            weight_exp = torch.pow(weight_exp[:, :, None], self.to_k(freq_embed))
            x = torch.einsum("bnmc,nmc -> bnmc", x, weight_exp) # exp decay
        
        x = F.conv1d(x.contiguous().view(B, N, -1), weight_cosn.t().contiguous().view(H, N, 1))
        x = F.conv1d(x.contiguous().view(-1, M, C), weight_cosm.t().contiguous().view(W, M, 1)).contiguous().view(B, H, W, -1)

        x = self.out_norm(x)
        
        x = x * nn.functional.silu(z)
        x = self.out_linear(x)

        x = x.permute(0, 3, 1, 2).contiguous()

        return x


class ThermalRadiationModule(nn.Module):
    """热辐射模块：实现通道间的温度交互"""

    def __init__(self, channels, temperature_scale=1.0, radiation_strength=0.1, alpha=1.0):
        super().__init__()
        self.channels = channels
        self.temperature_scale = temperature_scale
        self.radiation_strength = radiation_strength
        self.alpha = alpha

        # 学习每个通道的辐射系数（类似物理中的发射率ε）
        self.emissivity = nn.Parameter(torch.ones(channels))

        # 通道间交互的权重矩阵
        self.channel_interaction = nn.Linear(channels, channels, bias=False)

        # 自适应温度预测网络
        self.temperature_predictor = nn.Sequential(
            nn.AdaptiveAvgPool2d(1),
            nn.Conv2d(channels, channels // 4, 1),
            nn.ReLU(),
            nn.Conv2d(channels // 4, channels, 1),
            nn.Sigmoid()
        )

    def compute_channel_temperatures(self, x):
        """计算每个通道的全局平均温度"""
        # x: [B, C, H, W] -> temperatures: [B, C]
        temperatures = torch.mean(x, dim=[2, 3])  # 全局平均池化

        # 预测每个通道的温度权重
        temp_weights = self.temperature_predictor(x).squeeze(-1).squeeze(-1)  # [B, C]

        # 应用温度权重和缩放
        adaptive_temperatures = temperatures * temp_weights * self.temperature_scale
        return adaptive_temperatures

    def thermal_radiation_exchange(self, temperatures):
        """
        模拟热辐射交换过程 - 符合物理原理的实现

        物理原理：
        1. 能量守恒：总能量保持不变
        2. 热流方向：能量只能从高温通道流向低温通道
        3. 无自辐射：通道不能向自己辐射能量
        4. Stefan-Boltzmann定律：辐射功率与温度四次方成正比
        5. 温度差驱动：温度差越大，热交换越强烈
        """
        B, C = temperatures.shape
        device = temperatures.device

        # 计算每个通道的基础辐射功率（简化的Stefan-Boltzmann定律）
        base_power = self.emissivity.unsqueeze(0) * (temperatures.abs() + 1e-8)  # [B, C]

        # 初始化净能量变化
        net_energy_change = torch.zeros_like(temperatures)  # [B, C]

        # 计算所有通道对之间的热交换
        for i in range(C):
            for j in range(C):
                if i != j:  # 不考虑自辐射
                    # 计算温度差：T_i - T_j
                    temp_diff = temperatures[:, i] - temperatures[:, j]  # [B]

                    # 只有当T_i > T_j时才有从i到j的热流
                    positive_temp_diff = torch.relu(temp_diff)  # [B]

                    # 计算传递系数：使用温度差的平方根来平衡强度和方向
                    # 这样既保证温度差越大交换越强，又避免指数衰减过快
                    transfer_coeff = torch.exp(-self.alpha * torch.abs(temp_diff)) * torch.sqrt(positive_temp_diff + 1e-8)  # [B]

                    # 从通道i到通道j的能量流 = transfer_coeff * power_i
                    energy_flow = transfer_coeff * base_power[:, i]  # [B]

                    # 更新净能量变化
                    net_energy_change[:, i] -= energy_flow  # 通道i失去能量
                    net_energy_change[:, j] += energy_flow  # 通道j获得能量

        # 确保严格的能量守恒：总的净能量变化必须为0
        total_energy_change = torch.sum(net_energy_change, dim=1, keepdim=True)  # [B, 1]
        net_energy_change = net_energy_change - total_energy_change / C  # 平均分配误差

        return net_energy_change * self.radiation_strength

    def forward(self, x):
        """前向传播"""
        B, C, H, W = x.shape

        # 1. 计算通道温度
        temperatures = self.compute_channel_temperatures(x)

        # 2. 热辐射交换
        radiation_adjustment = self.thermal_radiation_exchange(temperatures)

        # 3. 应用辐射调整到特征图
        # 将标量调整广播到整个特征图
        adjustment_map = radiation_adjustment.unsqueeze(-1).unsqueeze(-1)  # [B, C, 1, 1]

        # 4. 通道间信息交互
        channel_weights = self.channel_interaction(temperatures)  # [B, C]
        channel_weights = torch.softmax(channel_weights, dim=1)

        # 5. 融合原始特征和辐射调整
        x_adjusted = x + adjustment_map
        x_reweighted = x_adjusted * channel_weights.unsqueeze(-1).unsqueeze(-1)

        return x_reweighted


class HeatBlock(nn.Module):
    def __init__(
        self,
        res: int = 14,
        infer_mode = False,
        hidden_dim: int = 0,
        drop_path: float = 0,
        norm_layer: Callable[..., torch.nn.Module] = partial(nn.LayerNorm, eps=1e-6),
        use_checkpoint: bool = False,
        drop: float = 0.0,
        act_layer: nn.Module = nn.GELU,
        mlp_ratio: float = 4.0,
        post_norm = True,
        layer_scale = None,
        **kwargs,
    ):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.norm1 = norm_layer(hidden_dim)
        self.op = Heat2D(res=res, dim=hidden_dim, hidden_dim=hidden_dim, infer_mode=infer_mode)
        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.mlp_branch = mlp_ratio > 0
        if self.mlp_branch:
            self.norm2 = norm_layer(hidden_dim)
            mlp_hidden_dim = int(hidden_dim * mlp_ratio)
            self.mlp = Mlp(in_features=hidden_dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop, channels_first=True)
        self.post_norm = post_norm
        self.layer_scale = layer_scale is not None

        self.infer_mode = infer_mode

        if self.layer_scale:
            self.gamma1 = nn.Parameter(layer_scale * torch.ones(hidden_dim),
                                       requires_grad=True)
            self.gamma2 = nn.Parameter(layer_scale * torch.ones(hidden_dim),
                                       requires_grad=True)


class EnhancedHeatBlock(nn.Module):
    """增强的Heat块：集成空间热传导和通道热辐射"""

    def __init__(
        self,
        res: int = 14,
        infer_mode = False,
        hidden_dim: int = 0,
        drop_path: float = 0,
        norm_layer: Callable[..., torch.nn.Module] = partial(nn.LayerNorm, eps=1e-6),
        use_checkpoint: bool = False,
        drop: float = 0.0,
        act_layer: nn.Module = nn.GELU,
        mlp_ratio: float = 4.0,
        post_norm = True,
        layer_scale = None,
        enable_thermal_radiation: bool = True,
        radiation_strength: float = 0.1,
        **kwargs,
    ):
        super().__init__()
        self.use_checkpoint = use_checkpoint
        self.enable_thermal_radiation = enable_thermal_radiation

        # 原始的空间热传导算子
        self.norm1 = norm_layer(hidden_dim)
        self.spatial_heat_conduction = Heat2D(res=res, dim=hidden_dim, hidden_dim=hidden_dim, infer_mode=infer_mode)

        # 为了兼容性，添加op属性指向spatial_heat_conduction
        self.op = self.spatial_heat_conduction

        # 新增的通道热辐射模块
        if self.enable_thermal_radiation:
            self.channel_thermal_radiation = ThermalRadiationModule(
                channels=hidden_dim,
                temperature_scale=1.0,
                radiation_strength=radiation_strength
            )

        self.drop_path = DropPath(drop_path) if drop_path > 0. else nn.Identity()
        self.mlp_branch = mlp_ratio > 0

        if self.mlp_branch:
            self.norm2 = norm_layer(hidden_dim)
            mlp_hidden_dim = int(hidden_dim * mlp_ratio)
            self.mlp = Mlp(in_features=hidden_dim, hidden_features=mlp_hidden_dim, act_layer=act_layer, drop=drop, channels_first=True)

        self.post_norm = post_norm
        self.layer_scale = layer_scale is not None
        self.infer_mode = infer_mode

        if self.layer_scale:
            self.gamma1 = nn.Parameter(layer_scale * torch.ones(hidden_dim), requires_grad=True)
            self.gamma2 = nn.Parameter(layer_scale * torch.ones(hidden_dim), requires_grad=True)
            if self.enable_thermal_radiation:
                self.gamma3 = nn.Parameter(layer_scale * torch.ones(hidden_dim), requires_grad=True)

    def _forward(self, x: torch.Tensor, freq_embed):
        """增强的前向传播：先空间热传导，再通道热辐射"""

        # 1. 空间热传导（原vHeat机制）- 使用与原始HeatBlock相同的逻辑
        if not self.layer_scale:
            if self.post_norm:
                x = x + self.drop_path(self.norm1(self.op(x, freq_embed)))
            else:
                x = x + self.drop_path(self.op(self.norm1(x), freq_embed))
        else:
            if self.post_norm:
                x = x + self.drop_path(self.gamma1[:, None, None] * self.norm1(self.op(x, freq_embed)))
            else:
                x = x + self.drop_path(self.gamma1[:, None, None] * self.op(self.norm1(x), freq_embed))

        # 2. 通道热辐射（新增机制）
        if self.enable_thermal_radiation:
            if not self.layer_scale:
                x_thermal_enhanced = self.channel_thermal_radiation(x)
                x = x + self.drop_path(x_thermal_enhanced - x)
            else:
                x_thermal_enhanced = self.channel_thermal_radiation(x)
                x = x + self.drop_path(self.gamma3[:, None, None] * (x_thermal_enhanced - x))

        # 3. MLP前馈网络
        if self.mlp_branch:
            if not self.layer_scale:
                if self.post_norm:
                    x = x + self.drop_path(self.norm2(self.mlp(x)))
                else:
                    x = x + self.drop_path(self.mlp(self.norm2(x)))
            else:
                if self.post_norm:
                    x = x + self.drop_path(self.gamma2[:, None, None] * self.norm2(self.mlp(x)))
                else:
                    x = x + self.drop_path(self.gamma2[:, None, None] * self.mlp(self.norm2(x)))

        return x

    def forward(self, input: torch.Tensor, freq_embed=None):
        if self.use_checkpoint:
            return checkpoint.checkpoint(self._forward, input, freq_embed)
        else:
            return self._forward(input, freq_embed)

    def _forward(self, x: torch.Tensor, freq_embed):
        if not self.layer_scale:
            if self.post_norm:
                x = x + self.drop_path(self.norm1(self.op(x, freq_embed)))
                if self.mlp_branch:
                    x = x + self.drop_path(self.norm2(self.mlp(x))) # FFN
            else:
                x = x + self.drop_path(self.op(self.norm1(x), freq_embed))
                if self.mlp_branch:
                    x = x + self.drop_path(self.mlp(self.norm2(x))) # FFN
            return x
        if self.post_norm:
            x = x + self.drop_path(self.gamma1[:, None, None] * self.norm1(self.op(x, freq_embed)))
            if self.mlp_branch:
                x = x + self.drop_path(self.gamma2[:, None, None] * self.norm2(self.mlp(x))) # FFN
        else:
            x = x + self.drop_path(self.gamma1[:, None, None] * self.op(self.norm1(x), freq_embed))
            if self.mlp_branch:
                x = x + self.drop_path(self.gamma2[:, None, None] * self.mlp(self.norm2(x))) # FFN
        return x
    
    def forward(self, input: torch.Tensor, freq_embed=None):
        if self.use_checkpoint:
            return checkpoint.checkpoint(self._forward, input, freq_embed)
        else:
            return self._forward(input, freq_embed)


class AdditionalInputSequential(nn.Sequential):
    def forward(self, x, *args, **kwargs):
        for module in self[:-1]:
            if isinstance(module, nn.Module):
                x = module(x, *args, **kwargs)
            else:
                x = module(x)
        x = self[-1](x)
        return x


class vHeat(nn.Module):
    def __init__(self, patch_size=4, in_chans=3, num_classes=1000, depths=[2, 2, 9, 2], 
                 dims=[96, 192, 384, 768], drop_path_rate=0.2, patch_norm=True, post_norm=True,
                 layer_scale=None, use_checkpoint=False, mlp_ratio=4.0, img_size=224,
                 act_layer='GELU', infer_mode=False, **kwargs):
        super().__init__()
        self.num_classes = num_classes
        self.num_layers = len(depths)
        if isinstance(dims, int):
            dims = [int(dims * 2 ** i_layer) for i_layer in range(self.num_layers)]
        self.embed_dim = dims[0]
        self.num_features = dims[-1]
        self.dims = dims
        
        self.depths = depths
        
        self.patch_embed = StemLayer(in_chans=in_chans,
                                     out_chans=self.embed_dim,
                                     act_layer='GELU',
                                     norm_layer='LN')
        
        res0 = img_size/patch_size
        self.res = [int(res0), int(res0//2), int(res0//4), int(res0//8)]
        
        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]  # stochastic depth decay rule
        
        self.infer_mode = infer_mode
        
        self.freq_embed = nn.ParameterList()
        for i in range(self.num_layers):
            self.freq_embed.append(nn.Parameter(torch.zeros(self.res[i], self.res[i], self.dims[i]), requires_grad=True))
            trunc_normal_(self.freq_embed[i], std=.02)
        
        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):
            self.layers.append(self.make_layer(
                res = self.res[i_layer],
                dim = self.dims[i_layer],
                depth = depths[i_layer],
                drop_path = dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],
                use_checkpoint=use_checkpoint,
                norm_layer=LayerNorm2d,
                post_norm=post_norm,
                layer_scale=layer_scale,
                downsample=self.make_downsample(
                    self.dims[i_layer], 
                    self.dims[i_layer + 1], 
                    norm_layer=LayerNorm2d,
                ) if (i_layer < self.num_layers - 1) else nn.Identity(),
                mlp_ratio=mlp_ratio,
                infer_mode=infer_mode,
            ))
            
        self.classifier = nn.Sequential(
            LayerNorm2d(self.num_features),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(1),
            nn.Linear(self.num_features, num_classes),
        )

        self.apply(self._init_weights)


class EnhancedvHeat(nn.Module):
    """增强版vHeat：集成空间热传导和通道热辐射"""

    def __init__(self, patch_size=4, in_chans=3, num_classes=1000, depths=[2, 2, 9, 2],
                 dims=[96, 192, 384, 768], drop_path_rate=0.2, patch_norm=True, post_norm=True,
                 layer_scale=None, use_checkpoint=False, mlp_ratio=4.0, img_size=224,
                 act_layer='GELU', infer_mode=False, enable_thermal_radiation=True,
                 radiation_strength=0.1, **kwargs):
        super().__init__()
        self.num_classes = num_classes
        self.num_layers = len(depths)
        if isinstance(dims, int):
            dims = [int(dims * 2 ** i_layer) for i_layer in range(self.num_layers)]
        self.embed_dim = dims[0]
        self.num_features = dims[-1]
        self.dims = dims
        self.depths = depths
        self.enable_thermal_radiation = enable_thermal_radiation

        self.patch_embed = StemLayer(in_chans=in_chans,
                                     out_chans=self.embed_dim,
                                     act_layer='GELU',
                                     norm_layer='LN')

        res0 = img_size/patch_size
        self.res = [int(res0), int(res0//2), int(res0//4), int(res0//8)]

        dpr = [x.item() for x in torch.linspace(0, drop_path_rate, sum(depths))]  # stochastic depth decay rule

        self.infer_mode = infer_mode

        self.freq_embed = nn.ParameterList()
        for i in range(self.num_layers):
            self.freq_embed.append(nn.Parameter(torch.zeros(self.res[i], self.res[i], self.dims[i]), requires_grad=True))
            trunc_normal_(self.freq_embed[i], std=.02)

        self.layers = nn.ModuleList()
        for i_layer in range(self.num_layers):
            self.layers.append(self.make_enhanced_layer(
                res = self.res[i_layer],
                dim = self.dims[i_layer],
                depth = depths[i_layer],
                drop_path = dpr[sum(depths[:i_layer]):sum(depths[:i_layer + 1])],
                use_checkpoint=use_checkpoint,
                norm_layer=LayerNorm2d,
                post_norm=post_norm,
                layer_scale=layer_scale,
                downsample=self.make_downsample(
                    self.dims[i_layer],
                    self.dims[i_layer + 1],
                    norm_layer=LayerNorm2d,
                ) if (i_layer < self.num_layers - 1) else nn.Identity(),
                mlp_ratio=mlp_ratio,
                infer_mode=infer_mode,
                enable_thermal_radiation=enable_thermal_radiation,
                radiation_strength=radiation_strength,
            ))

        self.classifier = nn.Sequential(
            LayerNorm2d(self.num_features),
            nn.AdaptiveAvgPool2d(1),
            nn.Flatten(1),
            nn.Linear(self.num_features, num_classes),
        )

        self.apply(self._init_weights)

    @staticmethod
    def make_downsample(dim=96, out_dim=192, norm_layer=LayerNorm2d):
        return nn.Sequential(
            nn.Conv2d(dim, out_dim, kernel_size=3, stride=2, padding=1, bias=False),
            norm_layer(out_dim)
        )

    @staticmethod
    def make_enhanced_layer(
        res=14,
        dim=96,
        depth=2,
        drop_path=[0.1, 0.1],
        use_checkpoint=False,
        norm_layer=LayerNorm2d,
        post_norm=True,
        layer_scale=None,
        downsample=nn.Identity(),
        mlp_ratio=4.0,
        infer_mode=False,
        enable_thermal_radiation=True,
        radiation_strength=0.1,
        **kwargs,
    ):
        blocks = []
        for i in range(depth):
            blocks.append(EnhancedHeatBlock(
                res=res,
                hidden_dim=dim,
                drop_path=drop_path[i] if isinstance(drop_path, list) else drop_path,
                use_checkpoint=use_checkpoint,
                norm_layer=norm_layer,
                post_norm=post_norm,
                layer_scale=layer_scale,
                mlp_ratio=mlp_ratio,
                infer_mode=infer_mode,
                enable_thermal_radiation=enable_thermal_radiation,
                radiation_strength=radiation_strength,
            ))
        blocks.append(downsample)
        return AdditionalInputSequential(*blocks)

    def _init_weights(self, m):
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
        elif isinstance(m, nn.Conv2d):
            fan_out = m.kernel_size[0] * m.kernel_size[1] * m.out_channels
            fan_out //= m.groups
            m.weight.data.normal_(0, math.sqrt(2.0 / fan_out))
            if m.bias is not None:
                m.bias.data.zero_()

    def infer_init(self):
        for i, layer in enumerate(self.layers):
            for block in layer[:-1]:
                if hasattr(block, 'op'):
                    block.op.infer_init_heat2d(self.freq_embed[i])
                elif hasattr(block, 'spatial_heat_conduction'):
                    block.spatial_heat_conduction.infer_init_heat2d(self.freq_embed[i])
        del self.freq_embed

    def forward_features(self, x):
        x = self.patch_embed(x)
        if self.infer_mode:
            for layer in self.layers:
                x = layer(x)
        else:
            for i, layer in enumerate(self.layers):
                x = layer(x, self.freq_embed[i]) # (B, C, H, W)
        return x

    def forward(self, x):
        x = self.forward_features(x)
        x = self.classifier(x)
        return x

    @staticmethod
    def make_downsample(dim=96, out_dim=192, norm_layer=LayerNorm2d):
        return nn.Sequential(
            #norm_layer(dim),
            #nn.Conv2d(dim, out_dim, kernel_size=2, stride=2)
            nn.Conv2d(dim, out_dim, kernel_size=3, stride=2, padding=1, bias=False),
            norm_layer(out_dim)
        )

    @staticmethod
    def make_layer(
        res=14,
        dim=96, 
        depth=2,
        drop_path=[0.1, 0.1], 
        use_checkpoint=False, 
        norm_layer=LayerNorm2d,
        post_norm=True,
        layer_scale=None,
        downsample=nn.Identity(), 
        mlp_ratio=4.0,
        infer_mode=False,
        **kwargs,
    ):
        assert depth == len(drop_path)
        blocks = []
        for d in range(depth):
            blocks.append(HeatBlock(
                res=res,
                hidden_dim=dim, 
                drop_path=drop_path[d],
                norm_layer=norm_layer,
                use_checkpoint=use_checkpoint,
                mlp_ratio=mlp_ratio,
                post_norm=post_norm,
                layer_scale=layer_scale,
                infer_mode=infer_mode,
            ))
        
        return AdditionalInputSequential(
            *blocks, 
            downsample,
        )
 
    def _init_weights(self, m: nn.Module):
        """
        out_proj.weight which is previously initilized in VSSBlock, would be cleared in nn.Linear
        no fc.weight found in the any of the model parameters
        no nn.Embedding found in the any of the model parameters
        so the thing is, VSSBlock initialization is useless
        
        Conv2D is not intialized !!!
        """
        # print(m, getattr(getattr(m, "weight", nn.Identity()), "INIT", None), isinstance(m, nn.Linear), "======================")
        if isinstance(m, nn.Linear):
            trunc_normal_(m.weight, std=.02)
            if isinstance(m, nn.Linear) and m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def infer_init(self):
        for i, layer in enumerate(self.layers):
            for block in layer[:-1]:
                block.op.infer_init_heat2d(self.freq_embed[i])
        del self.freq_embed
    
    def forward_features(self, x):
        x = self.patch_embed(x)
        if self.infer_mode:
            for layer in self.layers:
                x = layer(x)
        else:
            for i, layer in enumerate(self.layers):
                x = layer(x, self.freq_embed[i]) # (B, C, H, W)
        return x

    def forward(self, x):
        x = self.forward_features(x)
        x = self.classifier(x)
        return x


def test_enhanced_vheat():
    """测试增强版vHeat模型"""
    print("=== 测试增强版vHeat模型 ===")

    # 创建增强版模型
    model = EnhancedvHeat(
        patch_size=4,
        in_chans=3,
        num_classes=1000,
        depths=[2, 2, 6, 2],  # 减少深度以便快速测试
        dims=[96, 192, 384, 768],
        drop_path_rate=0.1,
        enable_thermal_radiation=True,
        radiation_strength=0.1,
        img_size=224
    )

    # 创建测试输入
    batch_size = 2
    input_tensor = torch.randn(batch_size, 3, 224, 224)

    print(f"输入形状: {input_tensor.shape}")
    print(f"模型参数数量: {sum(p.numel() for p in model.parameters() if p.requires_grad):,}")

    # 前向传播
    model.eval()
    with torch.no_grad():
        output = model(input_tensor)

    print(f"输出形状: {output.shape}")
    print(f"输出范围: [{output.min().item():.4f}, {output.max().item():.4f}]")

    # 测试热辐射模块
    print("\n=== 测试热辐射模块 ===")
    thermal_module = ThermalRadiationModule(channels=96)
    test_features = torch.randn(2, 96, 56, 56)

    print(f"输入特征形状: {test_features.shape}")

    # 计算通道温度
    temperatures = thermal_module.compute_channel_temperatures(test_features)
    print(f"通道温度形状: {temperatures.shape}")
    print(f"温度范围: [{temperatures.min().item():.4f}, {temperatures.max().item():.4f}]")

    # 热辐射交换
    radiation_energy = thermal_module.thermal_radiation_exchange(temperatures)
    print(f"辐射能量形状: {radiation_energy.shape}")
    print(f"辐射能量范围: [{radiation_energy.min().item():.4f}, {radiation_energy.max().item():.4f}]")

    # 完整前向传播
    enhanced_features = thermal_module(test_features)
    print(f"增强特征形状: {enhanced_features.shape}")

    print("\n=== 测试完成 ===")


def compare_models():
    """比较原始vHeat和增强vHeat的性能"""
    print("=== 模型比较 ===")

    # 创建相同配置的模型
    config = {
        'patch_size': 4,
        'in_chans': 3,
        'num_classes': 1000,
        'depths': [2, 2, 4, 2],  # 减少深度
        'dims': [96, 192, 384, 768],
        'drop_path_rate': 0.1,
        'img_size': 224
    }

    # 原始模型
    original_model = vHeat(**config)

    # 增强模型
    enhanced_model = EnhancedvHeat(
        **config,
        enable_thermal_radiation=True,
        radiation_strength=0.1
    )

    # 计算参数数量
    original_params = sum(p.numel() for p in original_model.parameters() if p.requires_grad)
    enhanced_params = sum(p.numel() for p in enhanced_model.parameters() if p.requires_grad)

    print(f"原始vHeat参数数量: {original_params:,}")
    print(f"增强vHeat参数数量: {enhanced_params:,}")
    print(f"参数增加: {enhanced_params - original_params:,} ({(enhanced_params/original_params-1)*100:.2f}%)")

    # 测试推理时间
    input_tensor = torch.randn(1, 3, 224, 224)

    # 原始模型推理
    original_model.eval()
    with torch.no_grad():
        start_time = time.time()
        for _ in range(10):
            _ = original_model(input_tensor)
        original_time = (time.time() - start_time) / 10

    # 增强模型推理
    enhanced_model.eval()
    with torch.no_grad():
        start_time = time.time()
        for _ in range(10):
            _ = enhanced_model(input_tensor)
        enhanced_time = (time.time() - start_time) / 10

    print(f"原始vHeat推理时间: {original_time*1000:.2f}ms")
    print(f"增强vHeat推理时间: {enhanced_time*1000:.2f}ms")
    print(f"时间增加: {(enhanced_time/original_time-1)*100:.2f}%")


if __name__ == "__main__":
    # 运行测试
    test_enhanced_vheat()
    print("\n" + "="*50 + "\n")
    compare_models()



