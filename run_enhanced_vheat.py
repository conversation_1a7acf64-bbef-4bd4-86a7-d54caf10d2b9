#!/usr/bin/env python3
"""
增强版vHeat训练启动脚本
简化的接口，避免分布式训练的复杂性
"""

import os
import sys
import argparse

def main():
    parser = argparse.ArgumentParser(description='Enhanced vHeat Training Launcher')
    
    # 数据相关参数
    parser.add_argument('--data-path', type=str, required=True,
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--output', type=str, default='./output',
                        help='Output directory')
    
    # 模型相关参数
    parser.add_argument('--model-size', type=str, default='tiny', 
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--model-type', type=str, default='enhanced', 
                        choices=['original', 'enhanced'],
                        help='Model type: original vHeat or enhanced vHeat (default: enhanced)')
    parser.add_argument('--radiation-strength', type=float, default=None,
                        help='Thermal radiation strength (overrides config default)')
    parser.add_argument('--disable-thermal-radiation', action='store_true',
                        help='Disable thermal radiation mechanism')
    
    # 训练相关参数
    parser.add_argument('--batch-size', type=int, default=None,
                        help='Batch size (default: from config)')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs (default: 100)')
    parser.add_argument('--lr', type=float, default=None,
                        help='Learning rate (default: from config)')
    parser.add_argument('--resume', type=str, default='',
                        help='Resume from checkpoint')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')
    
    args = parser.parse_args()
    
    # 验证数据路径
    if not os.path.exists(args.data_path):
        print(f"Error: Data path does not exist: {args.data_path}")
        return 1
    
    # 选择配置文件
    if args.model_type == 'enhanced':
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
            default_batch_size = 256
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_small_imagenet100.yaml'
            default_batch_size = 128
        else:  # base
            config_file = 'classification/configs/vHeat/EnhancedvHeat_base_imagenet100.yaml'
            default_batch_size = 64
    else:
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
            default_batch_size = 256
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
            default_batch_size = 128
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
            default_batch_size = 64
    
    if not os.path.exists(config_file):
        print(f"Error: Config file does not exist: {config_file}")
        return 1
    
    # 设置默认值
    batch_size = args.batch_size if args.batch_size is not None else default_batch_size
    
    print("="*60)
    print("Enhanced vHeat Training")
    print("="*60)
    print(f"Config file: {config_file}")
    print(f"Model type: {args.model_type}")
    print(f"Model size: {args.model_size}")
    print(f"Data path: {args.data_path}")
    print(f"Output: {args.output}")
    print(f"Batch size: {batch_size}")
    print(f"Epochs: {args.epochs}")
    
    if args.model_type == 'enhanced':
        print(f"Thermal radiation: {'Disabled' if args.disable_thermal_radiation else 'Enabled'}")
        if args.radiation_strength is not None:
            print(f"Radiation strength: {args.radiation_strength}")
    
    print("="*60)
    
    # 如果需要修改配置文件中的热辐射设置
    if args.model_type == 'enhanced' and (args.disable_thermal_radiation or args.radiation_strength is not None):
        import yaml
        import tempfile
        
        # 读取原配置
        with open(config_file, 'r') as f:
            config_data = yaml.safe_load(f)
        
        # 修改热辐射设置
        if args.disable_thermal_radiation:
            config_data['MODEL']['VHEAT']['ENABLE_THERMAL_RADIATION'] = False
        
        if args.radiation_strength is not None:
            config_data['MODEL']['VHEAT']['RADIATION_STRENGTH'] = args.radiation_strength
        
        # 创建临时配置文件
        temp_config = tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False)
        yaml.dump(config_data, temp_config, default_flow_style=False)
        temp_config.close()
        config_file = temp_config.name
        print(f"Using modified config: {config_file}")
    
    # 构建训练命令
    cmd = [
        sys.executable, 'train_single_gpu.py',
        '--data-path', args.data_path,
        '--output', args.output,
        '--config', config_file,
        '--batch-size', str(batch_size),
        '--epochs', str(args.epochs),
        '--num-classes', '100'  # ImageNet100固定为100类
    ]
    
    if args.lr is not None:
        cmd.extend(['--lr', str(args.lr)])
    
    if args.resume:
        cmd.extend(['--resume', args.resume])
    
    if args.eval_only:
        cmd.append('--eval-only')
    
    # 设置环境变量
    env = os.environ.copy()
    env['CUDA_VISIBLE_DEVICES'] = '0'  # 只使用第一个GPU
    
    print(f"Running command: {' '.join(cmd)}")
    print("="*60)
    
    # 执行训练
    import subprocess
    try:
        result = subprocess.run(cmd, env=env)
        return result.returncode
    except KeyboardInterrupt:
        print("Training interrupted by user")
        return 1
    except Exception as e:
        print(f"Error: {e}")
        return 1
    finally:
        # 清理临时配置文件
        if 'temp_config' in locals() and os.path.exists(config_file):
            os.unlink(config_file)

if __name__ == "__main__":
    sys.exit(main())

# 使用示例:
# 
# # 增强版vHeat tiny模型 (推荐开始)
# python run_enhanced_vheat.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --output ./output_enhanced_tiny
# 
# # 增强版vHeat small模型
# python run_enhanced_vheat.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size small \
#     --output ./output_enhanced_small
# 
# # 原始vHeat对比
# python run_enhanced_vheat.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type original \
#     --model-size tiny \
#     --output ./output_original_tiny
# 
# # 消融实验：禁用热辐射
# python run_enhanced_vheat.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --disable-thermal-radiation \
#     --output ./output_no_radiation
# 
# # 自定义辐射强度
# python run_enhanced_vheat.py \
#     --data-path /root/lanyun-fs/imagenet100-split \
#     --model-type enhanced \
#     --model-size tiny \
#     --radiation-strength 0.2 \
#     --output ./output_strong_radiation
