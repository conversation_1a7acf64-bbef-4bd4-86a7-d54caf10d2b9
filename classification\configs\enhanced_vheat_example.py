"""
增强版vHeat模型配置示例
集成空间热传导和通道热辐射机制
"""

# 基础配置
base_config = {
    'patch_size': 4,
    'in_chans': 3,
    'num_classes': 1000,
    'img_size': 224,
    'drop_path_rate': 0.2,
    'mlp_ratio': 4.0,
    'post_norm': True,
    'layer_scale': None,
    'use_checkpoint': False,
}

# 增强版vHeat配置
enhanced_vheat_configs = {
    # Tiny版本
    'enhanced_vheat_tiny': {
        **base_config,
        'depths': [2, 2, 6, 2],
        'dims': [96, 192, 384, 768],
        'enable_thermal_radiation': True,
        'radiation_strength': 0.05,  # 较小的辐射强度
    },
    
    # Small版本
    'enhanced_vheat_small': {
        **base_config,
        'depths': [2, 2, 18, 2],
        'dims': [96, 192, 384, 768],
        'enable_thermal_radiation': True,
        'radiation_strength': 0.1,   # 中等辐射强度
    },
    
    # Base版本
    'enhanced_vheat_base': {
        **base_config,
        'depths': [2, 2, 18, 2],
        'dims': [128, 256, 512, 1024],
        'enable_thermal_radiation': True,
        'radiation_strength': 0.15,  # 较大的辐射强度
    },
    
    # 消融实验：仅空间热传导
    'vheat_spatial_only': {
        **base_config,
        'depths': [2, 2, 18, 2],
        'dims': [96, 192, 384, 768],
        'enable_thermal_radiation': False,  # 禁用热辐射
        'radiation_strength': 0.0,
    },
    
    # 消融实验：强辐射版本
    'enhanced_vheat_strong_radiation': {
        **base_config,
        'depths': [2, 2, 18, 2],
        'dims': [96, 192, 384, 768],
        'enable_thermal_radiation': True,
        'radiation_strength': 0.3,   # 很强的辐射强度
    },
}

# 训练配置
training_config = {
    'batch_size': 128,
    'epochs': 300,
    'warmup_epochs': 20,
    'base_lr': 4e-3,
    'weight_decay': 0.05,
    'layer_decay': 0.9,
    'drop_path_rate': 0.2,
    'mixup': 0.8,
    'cutmix': 1.0,
    'reprob': 0.25,
    'remode': 'pixel',
    'recount': 1,
    'resplit': False,
}

# 数据增强配置
data_config = {
    'input_size': 224,
    'interpolation': 'bicubic',
    'mean': [0.485, 0.456, 0.406],
    'std': [0.229, 0.224, 0.225],
    'crop_pct': 0.9,
    'color_jitter': 0.4,
    'auto_augment': 'rand-m9-mstd0.5-inc1',
    'random_erase_prob': 0.25,
    'random_erase_mode': 'pixel',
    'random_erase_count': 1,
}

def get_model_config(model_name='enhanced_vheat_small'):
    """获取指定模型的配置"""
    if model_name not in enhanced_vheat_configs:
        raise ValueError(f"Unknown model: {model_name}. Available: {list(enhanced_vheat_configs.keys())}")
    
    return enhanced_vheat_configs[model_name]

def print_model_info(model_name='enhanced_vheat_small'):
    """打印模型配置信息"""
    config = get_model_config(model_name)
    
    print(f"=== {model_name} 配置信息 ===")
    print(f"模型深度: {config['depths']}")
    print(f"通道维度: {config['dims']}")
    print(f"热辐射启用: {config['enable_thermal_radiation']}")
    print(f"辐射强度: {config['radiation_strength']}")
    print(f"输入尺寸: {config['img_size']}x{config['img_size']}")
    print(f"Patch大小: {config['patch_size']}x{config['patch_size']}")
    print(f"Drop Path率: {config['drop_path_rate']}")
    print(f"MLP比率: {config['mlp_ratio']}")
    
    # 估算参数数量
    total_blocks = sum(config['depths'])
    base_params = sum(d * d for d in config['dims']) * 0.001  # 粗略估算
    thermal_params = sum(config['dims']) * 0.0001 if config['enable_thermal_radiation'] else 0
    
    print(f"总块数: {total_blocks}")
    print(f"估算参数量: ~{base_params + thermal_params:.1f}M")
    print("="*40)

if __name__ == "__main__":
    # 打印所有模型配置
    for model_name in enhanced_vheat_configs.keys():
        print_model_info(model_name)
        print()
