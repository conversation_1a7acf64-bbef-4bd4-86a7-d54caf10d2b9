任务：修正vHeat模型中的ThermalRadiationModule热辐射模块

背景：
当前的ThermalRadiationModule实现存在物理建模错误：

通道会从自己接收辐射能量（对角线元素非零）
所有通道都只接收能量，违反能量守恒定律
高温通道没有相应的能量损失，不符合热力学原理
要求：
请重新实现 ThermalRadiationModule类中的 thermal_radiation_exchange方法，使其符合正确的物理原理：

能量守恒：总能量保持不变，高温通道失去能量，低温通道获得能量
热流方向：能量只能从高温通道流向低温通道
无自辐射：通道不能向自己辐射能量
Stefan-Boltzmann定律：辐射功率与温度四次方成正比
温度差驱动：温度差越大，热交换越强烈
具体实现要求：

计算通道间的净热流（双向交换的差值）
使用torch.relu(temp_diff)确保热流方向正确
传递系数使用exp(-alpha * |temp_diff|)
确保最终的net_energy_change总和为0（能量守恒）
保持原有的输入输出接口不变
验证要求：
实现后请验证：

最高温通道的净能量变化为负值
最低温通道的净能量变化为正值
所有通道净能量变化的总和接近0
温度差越大的通道间交换越强烈
请提供完整的修正后的 thermal_radiation_exchange方法实现。