"""
增强版vHeat模型使用示例
演示如何创建、训练和使用集成热辐射机制的vHeat模型
"""

import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import DataLoader, TensorDataset
import time
import numpy as np

# 导入模型
from models.vHeat import EnhancedvHeat, ThermalRadiationModule
from configs.enhanced_vheat_example import get_model_config, print_model_info

def create_enhanced_model(model_name='enhanced_vheat_small'):
    """创建增强版vHeat模型"""
    config = get_model_config(model_name)
    model = EnhancedvHeat(**config)
    return model, config

def analyze_thermal_radiation_behavior():
    """分析热辐射模块的行为"""
    print("=== 热辐射模块行为分析 ===")
    
    # 创建热辐射模块
    channels = 96
    thermal_module = ThermalRadiationModule(
        channels=channels,
        temperature_scale=1.0,
        radiation_strength=0.1
    )
    
    # 创建测试数据：模拟不同温度分布的特征图
    batch_size = 4
    height, width = 56, 56
    
    # 场景1：均匀分布
    uniform_features = torch.randn(batch_size, channels, height, width) * 0.5
    
    # 场景2：高对比度分布
    contrast_features = torch.randn(batch_size, channels, height, width)
    contrast_features[:, :channels//2] *= 2.0  # 前半部分通道温度更高
    
    # 场景3：稀疏激活
    sparse_features = torch.randn(batch_size, channels, height, width) * 0.1
    sparse_features[:, channels//4:channels//2] *= 5.0  # 只有部分通道高激活
    
    scenarios = [
        ("均匀分布", uniform_features),
        ("高对比度", contrast_features),
        ("稀疏激活", sparse_features)
    ]
    
    for name, features in scenarios:
        print(f"\n--- {name} ---")
        
        # 计算通道温度
        temperatures = thermal_module.compute_channel_temperatures(features)
        print(f"温度统计: 均值={temperatures.mean().item():.4f}, "
              f"标准差={temperatures.std().item():.4f}, "
              f"范围=[{temperatures.min().item():.4f}, {temperatures.max().item():.4f}]")
        
        # 热辐射交换
        radiation_energy = thermal_module.thermal_radiation_exchange(temperatures)
        print(f"辐射能量统计: 均值={radiation_energy.mean().item():.4f}, "
              f"标准差={radiation_energy.std().item():.4f}")
        
        # 完整前向传播
        enhanced_features = thermal_module(features)
        
        # 计算变化
        feature_change = torch.norm(enhanced_features - features, dim=[2, 3]).mean()
        print(f"特征变化幅度: {feature_change.item():.6f}")

def benchmark_models():
    """基准测试：比较不同配置的性能"""
    print("=== 模型性能基准测试 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 测试配置
    test_configs = [
        'enhanced_vheat_tiny',
        'vheat_spatial_only',
        'enhanced_vheat_small',
        'enhanced_vheat_strong_radiation'
    ]
    
    batch_size = 8
    input_tensor = torch.randn(batch_size, 3, 224, 224).to(device)
    
    results = []
    
    for config_name in test_configs:
        print(f"\n--- 测试 {config_name} ---")
        
        # 创建模型
        model, config = create_enhanced_model(config_name)
        model = model.to(device)
        model.eval()
        
        # 计算参数数量
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"总参数数量: {total_params:,}")
        print(f"可训练参数: {trainable_params:,}")
        
        # 测试推理时间
        warmup_runs = 5
        test_runs = 20
        
        # 预热
        with torch.no_grad():
            for _ in range(warmup_runs):
                _ = model(input_tensor)
        
        # 正式测试
        torch.cuda.synchronize() if device.type == 'cuda' else None
        start_time = time.time()
        
        with torch.no_grad():
            for _ in range(test_runs):
                output = model(input_tensor)
                torch.cuda.synchronize() if device.type == 'cuda' else None
        
        end_time = time.time()
        avg_time = (end_time - start_time) / test_runs
        
        print(f"平均推理时间: {avg_time*1000:.2f}ms")
        print(f"吞吐量: {batch_size/avg_time:.1f} samples/sec")
        print(f"输出形状: {output.shape}")
        
        results.append({
            'name': config_name,
            'params': trainable_params,
            'time': avg_time,
            'throughput': batch_size/avg_time,
            'thermal_enabled': config['enable_thermal_radiation']
        })
    
    # 打印汇总结果
    print("\n=== 性能汇总 ===")
    print(f"{'模型名称':<25} {'参数量(M)':<12} {'时间(ms)':<10} {'吞吐量':<12} {'热辐射':<8}")
    print("-" * 75)
    
    for result in results:
        print(f"{result['name']:<25} "
              f"{result['params']/1e6:<12.2f} "
              f"{result['time']*1000:<10.2f} "
              f"{result['throughput']:<12.1f} "
              f"{'✓' if result['thermal_enabled'] else '✗':<8}")

def simple_training_example():
    """简单的训练示例"""
    print("=== 简单训练示例 ===")
    
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model, config = create_enhanced_model('enhanced_vheat_tiny')
    model = model.to(device)
    
    # 创建虚拟数据集
    num_samples = 100
    num_classes = 10
    
    # 生成随机数据
    X = torch.randn(num_samples, 3, 224, 224)
    y = torch.randint(0, num_classes, (num_samples,))
    
    # 创建数据加载器
    dataset = TensorDataset(X, y)
    dataloader = DataLoader(dataset, batch_size=8, shuffle=True)
    
    # 设置优化器和损失函数
    optimizer = optim.AdamW(model.parameters(), lr=1e-4, weight_decay=0.01)
    criterion = nn.CrossEntropyLoss()
    
    # 训练几个epoch
    model.train()
    num_epochs = 3
    
    for epoch in range(num_epochs):
        total_loss = 0
        num_batches = 0
        
        for batch_idx, (data, target) in enumerate(dataloader):
            data, target = data.to(device), target.to(device)
            
            optimizer.zero_grad()
            
            # 前向传播
            output = model(data)
            
            # 调整输出维度以匹配目标类别数
            if output.size(1) != num_classes:
                # 添加一个线性层来调整输出维度
                if not hasattr(model, 'final_proj'):
                    model.final_proj = nn.Linear(output.size(1), num_classes).to(device)
                output = model.final_proj(output)
            
            loss = criterion(output, target)
            
            # 反向传播
            loss.backward()
            optimizer.step()
            
            total_loss += loss.item()
            num_batches += 1
            
            if batch_idx % 5 == 0:
                print(f"Epoch {epoch+1}/{num_epochs}, Batch {batch_idx+1}, Loss: {loss.item():.4f}")
        
        avg_loss = total_loss / num_batches
        print(f"Epoch {epoch+1} 平均损失: {avg_loss:.4f}")
    
    print("训练完成！")

def main():
    """主函数"""
    print("增强版vHeat模型演示")
    print("="*50)
    
    # 1. 打印模型配置信息
    print_model_info('enhanced_vheat_small')
    print()
    
    # 2. 分析热辐射行为
    analyze_thermal_radiation_behavior()
    print()
    
    # 3. 性能基准测试
    benchmark_models()
    print()
    
    # 4. 简单训练示例
    simple_training_example()

if __name__ == "__main__":
    main()
