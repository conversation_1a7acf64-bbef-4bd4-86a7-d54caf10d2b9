#!/usr/bin/env python3
"""
测试训练好的增强版vHeat模型
计算在验证集上的Top-1和Top-5准确率
"""

import os
import sys
import argparse
import time
import json
from pathlib import Path

# 添加classification目录到路径
sys.path.insert(0, 'classification')

import torch
import torch.nn as nn
from torch.utils.data import DataLoader, SequentialSampler
import torchvision.transforms as transforms
import torchvision.datasets as datasets
import numpy as np

def build_test_dataset(data_path, img_size=224):
    """构建测试数据集"""
    transform = transforms.Compose([
        transforms.Resize(int(img_size * 1.143)),  # 256 for 224
        transforms.CenterCrop(img_size),
        transforms.ToTensor(),
        transforms.Normalize(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225])
    ])
    
    val_dir = os.path.join(data_path, 'val')
    if not os.path.exists(val_dir):
        # 如果没有val目录，尝试test目录
        val_dir = os.path.join(data_path, 'test')
        if not os.path.exists(val_dir):
            raise ValueError(f"Neither 'val' nor 'test' directory found in {data_path}")
    
    dataset = datasets.ImageFolder(val_dir, transform=transform)
    return dataset

def load_model_from_checkpoint(checkpoint_path, config_path=None, num_classes=100):
    """从检查点加载模型"""
    print(f"Loading checkpoint from: {checkpoint_path}")
    
    # 加载检查点
    checkpoint = torch.load(checkpoint_path, map_location='cpu')
    
    # 从检查点中获取配置信息
    if 'config' in checkpoint:
        config_info = checkpoint['config']
        print(f"Checkpoint config: {config_info}")
    
    # 确定模型类型
    if config_path and os.path.exists(config_path):
        import yaml
        with open(config_path, 'r') as f:
            config_dict = yaml.safe_load(f)
        model_type = config_dict['MODEL']['TYPE']
        vheat_config = config_dict['MODEL']['VHEAT']
    else:
        # 从检查点推断配置（如果可能）
        print("Warning: No config file provided, using default configuration")
        model_type = 'EnhancedvHeat'  # 默认假设是增强版
        vheat_config = {
            'PATCH_SIZE': 4,
            'IN_CHANS': 3,
            'EMBED_DIM': [96, 192, 384, 768],  # tiny配置
            'DEPTHS': [2, 2, 6, 2],
            'MLP_RATIO': 4.0,
            'POST_NORM': False,
            'ENABLE_THERMAL_RADIATION': True,
            'RADIATION_STRENGTH': 0.05
        }
    
    # 构建模型
    print(f"Building model: {model_type}")
    
    if model_type == 'EnhancedvHeat':
        from models.vHeat import EnhancedvHeat
        model = EnhancedvHeat(
            patch_size=vheat_config['PATCH_SIZE'],
            in_chans=vheat_config['IN_CHANS'],
            num_classes=num_classes,
            depths=vheat_config['DEPTHS'],
            dims=vheat_config['EMBED_DIM'],
            drop_path_rate=0.0,  # 测试时不使用drop path
            mlp_ratio=vheat_config['MLP_RATIO'],
            post_norm=vheat_config.get('POST_NORM', False),
            img_size=224,
            enable_thermal_radiation=vheat_config.get('ENABLE_THERMAL_RADIATION', True),
            radiation_strength=vheat_config.get('RADIATION_STRENGTH', 0.1),
        )
    else:
        from models.vHeat import vHeat
        model = vHeat(
            patch_size=vheat_config['PATCH_SIZE'],
            in_chans=vheat_config['IN_CHANS'],
            num_classes=num_classes,
            depths=vheat_config['DEPTHS'],
            dims=vheat_config['EMBED_DIM'],
            drop_path_rate=0.0,
            mlp_ratio=vheat_config['MLP_RATIO'],
            post_norm=vheat_config.get('POST_NORM', False),
            img_size=224,
        )
    
    # 加载权重
    if 'model' in checkpoint:
        state_dict = checkpoint['model']
    else:
        state_dict = checkpoint
    
    # 处理可能的键名不匹配
    model_dict = model.state_dict()
    filtered_dict = {}
    
    for k, v in state_dict.items():
        if k in model_dict and model_dict[k].shape == v.shape:
            filtered_dict[k] = v
        else:
            print(f"Warning: Skipping parameter {k} (shape mismatch or not found)")
    
    model_dict.update(filtered_dict)
    model.load_state_dict(model_dict)
    
    print(f"Successfully loaded {len(filtered_dict)}/{len(state_dict)} parameters")
    
    return model, checkpoint

def accuracy(output, target, topk=(1,)):
    """计算top-k准确率"""
    with torch.no_grad():
        maxk = max(topk)
        batch_size = target.size(0)

        _, pred = output.topk(maxk, 1, True, True)
        pred = pred.t()
        correct = pred.eq(target.view(1, -1).expand_as(pred))

        res = []
        for k in topk:
            correct_k = correct[:k].reshape(-1).float().sum(0, keepdim=True)
            res.append(correct_k.mul_(100.0 / batch_size))
        return res

def test_model(model, dataloader, device, num_classes=100):
    """测试模型性能"""
    model.eval()
    
    top1_correct = 0
    top5_correct = 0
    total_samples = 0
    total_time = 0
    
    # 用于计算每个类别的准确率
    class_correct = torch.zeros(num_classes)
    class_total = torch.zeros(num_classes)
    
    print("Starting evaluation...")
    print(f"Total batches: {len(dataloader)}")
    
    with torch.no_grad():
        for batch_idx, (data, target) in enumerate(dataloader):
            data, target = data.to(device), target.to(device)
            
            # 记录推理时间
            start_time = time.time()
            output = model(data)
            end_time = time.time()
            
            total_time += (end_time - start_time)
            
            # 计算top-1和top-5准确率
            acc1, acc5 = accuracy(output, target, topk=(1, 5))
            
            batch_size = target.size(0)
            top1_correct += acc1.item() * batch_size / 100.0
            top5_correct += acc5.item() * batch_size / 100.0
            total_samples += batch_size
            
            # 计算每个类别的准确率
            _, pred = output.max(1)
            for i in range(batch_size):
                label = target[i].item()
                class_total[label] += 1
                if pred[i] == target[i]:
                    class_correct[label] += 1
            
            # 打印进度
            if batch_idx % 50 == 0:
                current_top1 = 100.0 * top1_correct / total_samples
                current_top5 = 100.0 * top5_correct / total_samples
                print(f"Batch {batch_idx}/{len(dataloader)}: "
                      f"Top-1: {current_top1:.2f}%, Top-5: {current_top5:.2f}%")
    
    # 计算最终结果
    top1_acc = 100.0 * top1_correct / total_samples
    top5_acc = 100.0 * top5_correct / total_samples
    avg_time_per_sample = total_time / total_samples * 1000  # ms
    
    # 计算每个类别的准确率
    class_accuracies = []
    for i in range(num_classes):
        if class_total[i] > 0:
            acc = 100.0 * class_correct[i] / class_total[i]
            class_accuracies.append(acc.item())
        else:
            class_accuracies.append(0.0)
    
    return {
        'top1_accuracy': top1_acc,
        'top5_accuracy': top5_acc,
        'total_samples': total_samples,
        'avg_inference_time_ms': avg_time_per_sample,
        'class_accuracies': class_accuracies
    }

def main():
    parser = argparse.ArgumentParser(description='Test Enhanced vHeat Model')
    
    # 必需参数
    parser.add_argument('--checkpoint', type=str, required=True,
                        help='Path to model checkpoint (e.g., best.pt)')
    parser.add_argument('--data-path', type=str, required=True,
                        help='Path to validation dataset')
    
    # 可选参数
    parser.add_argument('--config', type=str, default=None,
                        help='Path to config file (optional)')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size for testing (default: 64)')
    parser.add_argument('--num-classes', type=int, default=100,
                        help='Number of classes (default: 100)')
    parser.add_argument('--img-size', type=int, default=224,
                        help='Image size (default: 224)')
    parser.add_argument('--output', type=str, default=None,
                        help='Output file to save results (optional)')
    parser.add_argument('--device', type=str, default='auto',
                        help='Device to use (auto/cpu/cuda)')
    
    args = parser.parse_args()
    
    # 设置设备
    if args.device == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(args.device)
    
    print(f"Using device: {device}")
    
    # 验证文件路径
    if not os.path.exists(args.checkpoint):
        print(f"Error: Checkpoint file not found: {args.checkpoint}")
        return 1
    
    if not os.path.exists(args.data_path):
        print(f"Error: Data path not found: {args.data_path}")
        return 1
    
    # 构建数据集
    print("Building test dataset...")
    test_dataset = build_test_dataset(args.data_path, args.img_size)
    print(f"Test dataset: {len(test_dataset)} samples")
    print(f"Number of classes: {len(test_dataset.classes)}")
    
    # 更新类别数
    actual_num_classes = len(test_dataset.classes)
    if actual_num_classes != args.num_classes:
        print(f"Warning: Actual classes ({actual_num_classes}) != specified classes ({args.num_classes})")
        print(f"Using actual number of classes: {actual_num_classes}")
        args.num_classes = actual_num_classes
    
    # 构建数据加载器
    test_loader = DataLoader(
        test_dataset,
        batch_size=args.batch_size,
        sampler=SequentialSampler(test_dataset),
        num_workers=4,
        pin_memory=True,
        drop_last=False
    )
    
    # 加载模型
    try:
        model, checkpoint = load_model_from_checkpoint(
            args.checkpoint, args.config, args.num_classes
        )
        model = model.to(device)
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        print(f"Model parameters: {total_params:,}")
        
        if 'epoch' in checkpoint:
            print(f"Checkpoint epoch: {checkpoint['epoch']}")
        if 'best_acc' in checkpoint:
            print(f"Checkpoint best accuracy: {checkpoint['best_acc']:.2f}%")
        
    except Exception as e:
        print(f"Error loading model: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    # 测试模型
    print("\n" + "="*60)
    print("Starting model evaluation...")
    print("="*60)
    
    try:
        results = test_model(model, test_loader, device, args.num_classes)
        
        # 打印结果
        print("\n" + "="*60)
        print("EVALUATION RESULTS")
        print("="*60)
        print(f"Total samples: {results['total_samples']:,}")
        print(f"Top-1 Accuracy: {results['top1_accuracy']:.2f}%")
        print(f"Top-5 Accuracy: {results['top5_accuracy']:.2f}%")
        print(f"Average inference time: {results['avg_inference_time_ms']:.2f} ms/sample")
        print(f"Throughput: {1000.0/results['avg_inference_time_ms']:.1f} samples/sec")
        
        # 计算类别准确率统计
        class_accs = results['class_accuracies']
        print(f"\nPer-class accuracy statistics:")
        print(f"Mean: {np.mean(class_accs):.2f}%")
        print(f"Std:  {np.std(class_accs):.2f}%")
        print(f"Min:  {np.min(class_accs):.2f}%")
        print(f"Max:  {np.max(class_accs):.2f}%")
        
        # 保存结果
        if args.output:
            results_dict = {
                'checkpoint_path': args.checkpoint,
                'data_path': args.data_path,
                'total_samples': results['total_samples'],
                'top1_accuracy': results['top1_accuracy'],
                'top5_accuracy': results['top5_accuracy'],
                'avg_inference_time_ms': results['avg_inference_time_ms'],
                'class_accuracies': results['class_accuracies'],
                'model_parameters': total_params,
                'test_config': vars(args)
            }
            
            with open(args.output, 'w') as f:
                json.dump(results_dict, f, indent=2)
            print(f"\nResults saved to: {args.output}")
        
        print("="*60)
        
        return 0
        
    except Exception as e:
        print(f"Error during evaluation: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
