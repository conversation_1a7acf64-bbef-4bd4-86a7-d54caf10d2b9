#!/usr/bin/env python3
"""
ImageNet100训练脚本 - 支持原始vHeat和增强版vHeat
使用方法:

# 使用增强版vHeat (默认)
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-type enhanced --model-size tiny --batch-size 256 --output ./output

# 使用原始vHeat
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-type original --model-size tiny --batch-size 256 --output ./output

# 自定义热辐射强度
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-type enhanced --radiation-strength 0.2 --output ./output

# 禁用热辐射机制
python train_imagenet100.py --data-path /path/to/your/imagenet100 --model-type enhanced --disable-thermal-radiation --output ./output
"""

import os
import subprocess
import argparse

def main():
    parser = argparse.ArgumentParser(description='Train vHeat on ImageNet100')
    parser.add_argument('--data-path', type=str, required=True, 
                        help='Path to ImageNet100 dataset')
    parser.add_argument('--batch-size', type=int, default=64,
                        help='Batch size per GPU (default: 64)')
    parser.add_argument('--output', type=str, default='./output',
                        help='Output directory (default: ./output)')
    parser.add_argument('--gpus', type=int, default=1,
                        help='Number of GPUs to use (default: 1)')
    parser.add_argument('--model-size', type=str, default='tiny',
                        choices=['tiny', 'small', 'base'],
                        help='Model size (default: tiny)')
    parser.add_argument('--model-type', type=str, default='enhanced',
                        choices=['original', 'enhanced'],
                        help='Model type: original vHeat or enhanced vHeat (default: enhanced)')
    parser.add_argument('--radiation-strength', type=float, default=None,
                        help='Thermal radiation strength (overrides config default)')
    parser.add_argument('--disable-thermal-radiation', action='store_true',
                        help='Disable thermal radiation mechanism')
    parser.add_argument('--epochs', type=int, default=100,
                        help='Number of training epochs (default: 100)')
    parser.add_argument('--resume', type=str, default='',
                        help='Resume from checkpoint')
    parser.add_argument('--pretrained', type=str, default='',
                        help='Use pretrained weights')
    parser.add_argument('--eval-only', action='store_true',
                        help='Only evaluate the model')
    parser.add_argument('--single-gpu', action='store_true',
                        help='Use single GPU training (no distributed)')
    parser.add_argument('--disable-ema', action='store_true',
                        help='Disable EMA (useful for resuming from problematic checkpoints)')

    args = parser.parse_args()
    
    # 检查数据集路径
    if not os.path.exists(args.data_path):
        raise ValueError(f"Dataset path does not exist: {args.data_path}")
    
    train_path = os.path.join(args.data_path, 'train')
    val_path = os.path.join(args.data_path, 'val')
    
    if not os.path.exists(train_path):
        raise ValueError(f"Train directory does not exist: {train_path}")
    if not os.path.exists(val_path):
        raise ValueError(f"Validation directory does not exist: {val_path}")
    
    # 检查类别数
    train_classes = len([d for d in os.listdir(train_path) 
                        if os.path.isdir(os.path.join(train_path, d))])
    val_classes = len([d for d in os.listdir(val_path) 
                      if os.path.isdir(os.path.join(val_path, d))])
    
    print(f"Found {train_classes} classes in train set")
    print(f"Found {val_classes} classes in validation set")
    print(f"Model type: {args.model_type}")
    print(f"Model size: {args.model_size}")

    if args.model_type == 'enhanced':
        print(f"Thermal radiation: {'Disabled' if args.disable_thermal_radiation else 'Enabled'}")
        if args.radiation_strength is not None:
            print(f"Radiation strength: {args.radiation_strength}")

    if train_classes != val_classes:
        print(f"Warning: Train and validation have different number of classes!")
    
    # 选择配置文件
    if args.model_type == 'enhanced':
        # 使用增强版vHeat配置
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_tiny_imagenet100.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/EnhancedvHeat_small_imagenet100.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/EnhancedvHeat_base_imagenet100.yaml'
    else:
        # 使用原始vHeat配置
        if args.model_size == 'tiny':
            config_file = 'classification/configs/vHeat/vHeat_tiny_imagenet100.yaml'
        elif args.model_size == 'small':
            config_file = 'classification/configs/vHeat/vHeat_small_imagenet100.yaml'
        else:  # base
            config_file = 'classification/configs/vHeat/vHeat_base_imagenet100.yaml'
    
    # 构建训练命令
    if args.single_gpu or args.gpus == 1:
        # 单GPU训练，不使用分布式
        cmd = [
            'python', 'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output,
            '--local_rank', '0'
        ]
    else:
        # 多GPU分布式训练，使用torchrun
        cmd = [
            'torchrun',
            '--nnodes=1',
            '--nproc_per_node=' + str(args.gpus),
            '--master_addr=127.0.0.1',
            '--master_port=29501',
            'classification/main.py',
            '--cfg', config_file,
            '--batch-size', str(args.batch_size),
            '--data-path', args.data_path,
            '--output', args.output
        ]
    
    # 添加可选参数
    if args.resume:
        cmd.extend(['--resume', args.resume])
    
    if args.pretrained:
        cmd.extend(['--pretrained', args.pretrained])
    
    if args.eval_only:
        cmd.append('--eval')

    if args.disable_ema:
        cmd.extend(['--model_ema', 'False'])

    # 添加自定义配置选项
    opts = [
        'TRAIN.EPOCHS', str(args.epochs),
        'MODEL.NUM_CLASSES', str(train_classes)
    ]

    # 如果是增强版模型，添加热辐射相关配置
    if args.model_type == 'enhanced':
        if args.disable_thermal_radiation:
            opts.extend(['MODEL.VHEAT.ENABLE_THERMAL_RADIATION', 'False'])

        if args.radiation_strength is not None:
            opts.extend(['MODEL.VHEAT.RADIATION_STRENGTH', str(args.radiation_strength)])

    cmd.extend(['--opts'] + opts)
    
    print("Running command:")
    print(' '.join(cmd))
    print()
    
    # 执行训练
    try:
        subprocess.run(cmd, check=True)
    except subprocess.CalledProcessError as e:
        print(f"Training failed with error code {e.returncode}")
        return e.returncode
    
    print("Training completed successfully!")
    return 0

if __name__ == '__main__':
    exit(main())


# 使用示例:

# 1. 增强版vHeat tiny模型 (推荐)
# python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --batch-size 256 --single-gpu --output ./output_enhanced_tiny

# 2. 增强版vHeat small模型
# python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size small --batch-size 128 --single-gpu --output ./output_enhanced_small

# 3. 原始vHeat对比实验
# python train_imagenet100.py --data-path /path/to/imagenet100 --model-type original --model-size tiny --batch-size 256 --single-gpu --output ./output_original_tiny

# 4. 自定义辐射强度
# python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --radiation-strength 0.2 --single-gpu --output ./output_strong_radiation

# 5. 消融实验：禁用热辐射
# python train_imagenet100.py --data-path /path/to/imagenet100 --model-type enhanced --model-size tiny --disable-thermal-radiation --single-gpu --output ./output_no_radiation